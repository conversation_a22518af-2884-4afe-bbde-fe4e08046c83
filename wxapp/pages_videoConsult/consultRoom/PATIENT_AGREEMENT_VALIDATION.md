# 就诊人切换与知情同意书校验优化

## 功能概述

优化了就诊人切换和知情同意书勾选的逻辑，确保切换就诊人时重置协议状态，并在勾选知情同意书时校验就诊人的实名认证状态。

## 需求背景

### 原有问题

1. **状态不重置**：切换就诊人时，知情同意书的勾选状态没有重置，可能导致状态混乱
2. **缺少实名校验**：点击知情同意书时没有校验当前就诊人是否实名，可能导致未实名用户也能勾选协议

### 业务需求

1. **切换就诊人时重置状态**：每次切换就诊人都应该重置知情同意书的勾选状态
2. **实名校验**：只有完成实名认证（有身份证号码）的就诊人才能勾选知情同意书

## 解决方案

### 1. 切换就诊人时重置协议状态

#### 修改 selectPatient 方法

```javascript
selectPatient(e) {
  const index = e.currentTarget.dataset.index
  const selectedPatient = this.data.patientList[index] || null
  
  this.setData({
    currentPatientIndex: index,
    currentPatient: selectedPatient,
    // 切换就诊人时重置知情同意书的勾选状态
    checked: false,
    isAgreement: false
  })

  console.log('选择就诊人:', {
    index: index,
    patient: selectedPatient,
    resetAgreementStatus: '已重置知情同意书勾选状态'
  })

  // 自动滑动到选中的就诊人位置
  this.scrollToPatient(index)
}
```

**重置的状态**：
- `checked: false` - 取消知情同意书勾选
- `isAgreement: false` - 重置协议同意状态

### 2. 知情同意书勾选时的实名校验

#### 修改 onChecked 方法

```javascript
async onChecked(e) {
  const checked = e.detail

  if (checked) {
    // 勾选时先校验当前就诊人是否实名
    const isValidated = await this.validatePatientRealName()
    if (!isValidated) {
      // 实名校验失败，不允许勾选
      return
    }

    // 实名校验通过，设置勾选状态
    this.setData({
      checked: checked
    })

    // 如果还未同意过协议，则显示协议弹框
    if (!this.data.isAgreement) {
      this.showAgreementPopup()
    }
  } else {
    // 如果是取消勾选，重置协议同意状态
    this.setData({
      checked: checked,
      isAgreement: false
    })
  }
}
```

**校验流程**：

1. 用户点击勾选知情同意书
2. 调用 `validatePatientRealName()` 异步校验实名状态
3. 校验通过才允许勾选，否则显示错误信息的 toast

### 3. 实名认证校验逻辑

#### 修改 validatePatientRealName 方法

```javascript
async validatePatientRealName() {
  const currentPatient = this.data.currentPatient

  // 检查是否选择了就诊人
  if (!currentPatient) {
    wx.showToast({
      title: '请先选择就诊人',
      icon: 'none',
      duration: 3000
    })
    return false
  }

  try {
    // 显示loading
    wx.showLoading({
      title: '校验中...',
      mask: true
    })

    // 调用接口校验就诊人信息
    const params = {
      inquirerId: currentPatient.inquirerId,
      price: 0,
      conditionDesc: '无',
      offlineDiagnosis: '无',
      subOrderCode: this.data.subOrderCode,
      packageCode: this.data.packageCode
    }

    console.log('调用 validatePatientInfo 接口校验就诊人:', {
      patientName: currentPatient.name,
      inquirerId: currentPatient.inquirerId,
      params: params
    })

    const res = await util.request(api.validatePatientInfo, params, 'post', 1)

    wx.hideLoading()

    if (res.data.code === 0) {
      console.log('就诊人校验通过:', currentPatient.name)
      return true
    } else {
      console.log('就诊人校验失败:', res.data.msg)
      // 接口返回错误信息，直接toast显示
      wx.showToast({
        title: res.data.msg || '就诊人信息校验失败',
        icon: 'none',
        duration: 3000
      })
      return false
    }

  } catch (err) {
    console.error('校验就诊人信息失败:', err)
    wx.hideLoading()
    wx.showToast({
      title: '网络异常，请稍后重试',
      icon: 'none',
      duration: 3000
    })
    return false
  }
}
```

**校验标准**：
- 调用 `api.validatePatientInfo` 接口进行实名认证校验
- 接口返回 `code === 0` 表示校验通过
- 接口返回错误时直接显示错误信息的 toast

### 4. 接口参数说明

#### api.validatePatientInfo 接口

**请求参数**：
```javascript
{
  inquirerId: currentPatient.inquirerId,  // 就诊人ID
  price: 0,                               // 价格（固定为0）
  conditionDesc: '无',                    // 病情描述（固定为'无'）
  offlineDiagnosis: '无',                 // 线下诊断（固定为'无'）
  subOrderCode: this.data.subOrderCode,   // 子订单编码
  packageCode: this.data.packageCode      // 套餐编码
}
```

**响应格式**：
```javascript
{
  code: 0,        // 0表示校验通过，非0表示校验失败
  msg: "string",  // 错误信息（校验失败时显示）
  data: {}        // 响应数据
}
```

**校验逻辑**：

- 接口返回 `code === 0` 表示就诊人实名认证校验通过
- 接口返回 `code !== 0` 表示校验失败，直接显示 `msg` 字段的错误信息

## 用户交互流程

### 1. 切换就诊人流程

```
用户点击选择就诊人
↓
调用 selectPatient 方法
↓
设置新的当前就诊人
↓
重置知情同意书状态
├─ checked: false
└─ isAgreement: false
↓
滚动到选中的就诊人位置
```

### 2. 勾选知情同意书流程

```
用户点击勾选知情同意书
↓
调用 onChecked 方法
↓
校验当前就诊人是否选择
├─ 未选择 → 提示"请先选择就诊人"
└─ 已选择 → 继续校验
↓
调用 api.validatePatientInfo 接口校验实名状态
├─ 显示"校验中..."loading
├─ 校验失败 → 显示错误信息toast，取消勾选
└─ 校验通过 → 允许勾选
↓
设置勾选状态并显示协议弹框
```

### 3. 实名认证校验说明

**校验方式**：

- 调用 `api.validatePatientInfo` 接口进行实名认证校验
- 接口校验失败时直接显示后端返回的错误信息
- 不再显示弹框引导用户去编辑，而是通过toast提示具体的错误原因

## 数据状态管理

### 页面数据状态

```javascript
data: {
  checked: false,        // 知情同意书勾选状态
  isAgreement: false,    // 协议同意状态
  currentPatient: null,  // 当前选中的就诊人
  currentPatientIndex: 0 // 当前选中的就诊人索引
}
```

### 状态变化时机

1. **页面初始化**：
   - `checked: false`
   - `isAgreement: false`

2. **切换就诊人**：
   - `checked: false` （重置）
   - `isAgreement: false` （重置）

3. **勾选知情同意书**：
   - 实名校验通过：`checked: true`
   - 实名校验失败：保持 `checked: false`

4. **同意协议**：
   - `checked: true`
   - `isAgreement: true`

5. **取消勾选**：
   - `checked: false`
   - `isAgreement: false`

## 实名认证判断标准

### 判断依据

调用 `api.validatePatientInfo` 接口进行实名认证校验：

```javascript
const res = await util.request(api.validatePatientInfo, params, 'post', 1)
return res.data.code === 0
```

### 判断逻辑

1. **已实名**：接口返回 `code === 0`
2. **未实名**：接口返回 `code !== 0`，并显示 `msg` 字段的错误信息

### 优势

- **准确性**：由后端统一校验，确保校验逻辑的一致性
- **灵活性**：后端可以根据业务需求调整校验规则
- **用户友好**：直接显示后端返回的具体错误信息

## 错误处理

### 1. 未选择就诊人

```javascript
if (!currentPatient) {
  wx.showToast({
    title: '请先选择就诊人',
    icon: 'none',
    duration: 3000
  })
  return false
}
```

### 2. 未实名认证

```javascript
if (!hasIdCard) {
  wx.showModal({
    title: '实名认证提醒',
    content: `当前就诊人"${currentPatient.name}"尚未完成实名认证，请先完善身份证信息后再勾选知情同意书。`,
    showCancel: true,
    cancelText: '稍后处理',
    confirmText: '去完善',
    success: (res) => {
      if (res.confirm) {
        this.editCurrentPatient()
      }
    }
  })
  return false
}
```

## 测试场景

### 1. 切换就诊人测试

**步骤**：
1. 选择就诊人A，勾选知情同意书
2. 切换到就诊人B

**预期结果**：
- 知情同意书勾选状态被重置为未勾选
- 协议同意状态被重置

### 2. 实名认证校验测试

#### 场景1：实名认证通过
**步骤**：

1. 选择已完成实名认证的就诊人
2. 点击勾选知情同意书

**预期结果**：

- 显示"校验中..."loading
- 接口返回成功，允许勾选
- 显示协议弹框

#### 场景2：实名认证失败
**步骤**：

1. 选择未完成实名认证的就诊人
2. 点击勾选知情同意书

**预期结果**：

- 显示"校验中..."loading
- 接口返回失败，显示错误信息toast
- 不允许勾选，保持未勾选状态

### 3. 网络异常测试

**步骤**：

1. 断开网络连接
2. 选择就诊人并点击勾选知情同意书

**预期结果**：

- 显示"校验中..."loading
- 接口调用失败，显示"网络异常，请稍后重试"toast
- 不允许勾选

## 相关文件

- **主要文件**：`wxapp/pages_videoConsult/consultRoom/index.js`
- **修改方法**：
  - `selectPatient()` - 切换就诊人时重置协议状态
  - `onChecked()` - 勾选时校验实名状态
  - `validatePatientRealName()` - 新增实名校验方法
  - `editCurrentPatient()` - 新增编辑当前就诊人方法

## 总结

通过这次优化：

1. ✅ **状态重置**：切换就诊人时自动重置知情同意书状态
2. ✅ **实名校验**：只有实名认证的就诊人才能勾选协议
3. ✅ **用户引导**：未实名时提供明确的引导和操作入口
4. ✅ **状态一致**：确保协议状态与当前就诊人保持一致
5. ✅ **用户体验**：提供友好的提示和操作流程

现在用户在切换就诊人时会自动重置协议状态，并且只有完成实名认证的就诊人才能勾选知情同意书，确保了业务流程的合规性和用户体验的一致性。
