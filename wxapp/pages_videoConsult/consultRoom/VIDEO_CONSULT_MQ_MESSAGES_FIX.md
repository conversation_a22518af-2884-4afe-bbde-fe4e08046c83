# 视频问诊发起后MQ消息渲染功能完善

## 问题描述

发起视频问诊咨询后，后端会推送两条MQ消息：
1. `type=10015`：小助理问候语
2. `type=10016`：您发起了视频问诊

这两条消息需要正确渲染到 `pages_videoConsult/consultRoom/index` 页面上。

## 问题分析

原有的消息分发逻辑存在问题：
1. 发起视频问诊后，用户会跳转到呼叫医生页面（`pages_videoConsult/callDoctor/index`）
2. 后端推送的助理消息（type=10015, 10016）在用户位于呼叫页面时到达
3. 原有的消息分发逻辑只将视频问诊消息分发给视频咨询室页面，导致这些消息被忽略
4. 用户返回视频咨询室页面时看不到这些助理消息

## 解决方案

### 1. 修改消息分发逻辑

**文件**: `wxapp/app.js`

修改 `onMessageArrived` 方法中的消息分发逻辑：

```javascript
// 根据消息类型和页面类型进行分发
if (isVideoConsult) {
  // 视频问诊消息分发给视频咨询室页面或呼叫医生页面
  if (currentRoute === 'pages_videoConsult/consultRoom/index') {
    if (typeof currentPage.onMessageArrived === 'function') {
      console.log('向视频咨询室页面分发消息:', message)
      currentPage.onMessageArrived(message, len)
    }
  } else if (currentRoute === 'pages_videoConsult/callDoctor/index') {
    // 对于呼叫医生页面，只处理特定类型的消息（如助理消息）
    if ([10015, 10016].includes(message.type)) {
      console.log('向呼叫医生页面分发助理消息:', message)
      // 将消息缓存到全局，等返回视频咨询室页面时处理
      this.cacheVideoConsultMessage(message)
    }
  }
}
```

### 2. 添加消息缓存机制

**文件**: `wxapp/app.js`

添加缓存和获取视频问诊消息的方法：

```javascript
/**
 * 缓存视频问诊消息
 * 用于在呼叫医生页面时缓存助理消息，等返回视频咨询室页面时处理
 */
cacheVideoConsultMessage(message) {
  console.log('缓存视频问诊消息:', message)
  
  // 初始化缓存数组
  if (!this.globalData.cachedVideoConsultMessages) {
    this.globalData.cachedVideoConsultMessages = []
  }
  
  // 添加时间戳，确保消息有sendTime
  if (!message.sendTime) {
    message.sendTime = Date.now()
  }
  
  // 检查重复消息
  const isDuplicate = this.globalData.cachedVideoConsultMessages.some(cachedMsg => {
    return cachedMsg.id === message.id || 
           (cachedMsg.type === message.type && 
            cachedMsg.content === message.content && 
            Math.abs(cachedMsg.sendTime - message.sendTime) < 5000)
  })
  
  if (!isDuplicate) {
    this.globalData.cachedVideoConsultMessages.push(message)
  }
}

/**
 * 获取并清空缓存的视频问诊消息
 */
getCachedVideoConsultMessages() {
  const cachedMessages = this.globalData.cachedVideoConsultMessages || []
  this.globalData.cachedVideoConsultMessages = []
  return cachedMessages
}
```

### 3. 修改视频咨询室页面处理逻辑

**文件**: `wxapp/pages_videoConsult/consultRoom/index.js`

在 `onShow` 生命周期中添加缓存消息处理：

```javascript
async onShow() {
  // ... 其他逻辑

  // 处理缓存的视频问诊消息（如助理消息）
  this.processCachedVideoConsultMessages()

  // ... 其他逻辑
}
```

添加处理缓存消息的方法：

```javascript
/**
 * 处理缓存的视频问诊消息
 * 从全局缓存中获取在呼叫页面时收到的助理消息并处理
 */
processCachedVideoConsultMessages() {
  const app = getApp()
  const cachedMessages = app.getCachedVideoConsultMessages()
  
  if (cachedMessages.length === 0) {
    return
  }

  console.log('处理缓存的视频问诊消息，数量:', cachedMessages.length)

  // 逐个处理缓存的消息
  cachedMessages.forEach((message, index) => {
    // 确保消息有正确的consultType和consultId
    message.consultType = 2
    message.consultId = this.data.consultId || 'default'

    // 延迟处理每条消息，避免同时处理导致的问题
    setTimeout(() => {
      this.updateResult(message, () => {
        // 滚动到最新消息
        this.setData({
          scrollIntoView: 'chat_' + message.sendTime
        })
      })
    }, index * 100) // 每条消息间隔100ms处理
  })
}
```

### 4. 消息模板渲染

**文件**: `wxapp/pages_videoConsult/consultRoom/template/consult.wxml`

模板中已经包含了这两种消息类型的渲染逻辑：

```xml
<!-- 小助理问候语 -->
<view wx:if="{{item.type == 10015}}" class='record-chatting-item other' style='justify-content: flex-start'>
  <image src='{{img_customer_service_head}}' class='record-chatting-item-img mr20'></image>
  <view class='flex-column'>
    <view class="f24 c333 mb10">招商信诺助理-小诺</view>
    <view class='record-chatting-item-text receivetext f32 c333' style="max-width: 80%;">
      {{item.content}}
    </view>
  </view>
</view>

<!-- 您发起了视频问诊 -->
<view wx:if="{{item.type == 10016}}" class="f28 c333 m30 p20 flex_c"
      style='background: #EAEAEA;border-radius: 8rpx;'>
  {{item.content}}
</view>
```

### 5. 消息relation字段处理

确保这两种消息类型在各种处理场景中都被正确标记为医生发送：

```javascript
// 在 addMessageToList、getHistoryData、refreshLatestMessages 等方法中
if ([10005, 16, 17, 10006, 10007, 10008, 10009, 10010, 10011, 10012, 10013, 10015, 10016].includes(message.type)) {
  message.relation = 1 // 医生发送
}
```

## 完整流程

1. **用户发起视频问诊**：
   - 用户在视频咨询室页面点击呼叫医生
   - 调用 `videoPayInfo` 接口发起视频咨询
   - 跳转到呼叫医生页面

2. **后端推送助理消息**：
   - 后端推送 `type=10015`（小助理问候语）
   - 后端推送 `type=10016`（您发起了视频问诊）

3. **消息缓存处理**：
   - 由于用户在呼叫页面，消息被缓存到全局
   - 消息不会丢失，等待后续处理

4. **用户返回视频咨询室**：
   - 用户挂断或完成通话后返回视频咨询室页面
   - `onShow` 触发，调用 `processCachedVideoConsultMessages`
   - 从全局缓存获取助理消息并处理
   - 消息被添加到页面并正确渲染

5. **消息展示**：
   - `type=10015` 显示为带头像的助理消息
   - `type=10016` 显示为灰色背景的系统消息
   - 消息正确显示在医生消息区域（右侧）

## 测试要点

1. **发起视频问诊测试**：
   - 发起视频问诊后检查是否收到两条助理消息
   - 验证消息是否被正确缓存

2. **页面跳转测试**：
   - 从视频咨询室跳转到呼叫页面
   - 从呼叫页面返回视频咨询室
   - 验证缓存消息是否正确处理

3. **消息展示测试**：
   - 验证 `type=10015` 消息的头像和样式
   - 验证 `type=10016` 消息的系统样式
   - 验证消息的时间顺序和滚动位置

4. **重复消息测试**：
   - 验证重复消息不会被重复缓存
   - 验证缓存清空机制正常工作

## 相关文件

- `wxapp/app.js` - MQ消息分发和缓存逻辑
- `wxapp/pages_videoConsult/consultRoom/index.js` - 视频咨询室页面消息处理
- `wxapp/pages_videoConsult/consultRoom/template/consult.wxml` - 消息渲染模板

## 注意事项

1. **消息时序**：确保缓存消息按正确的时间顺序处理
2. **重复处理**：避免同一条消息被重复处理
3. **内存管理**：及时清空缓存，避免内存泄漏
4. **错误处理**：网络异常时不应影响消息缓存机制
5. **兼容性**：确保与现有的消息处理逻辑兼容
