# 视频问诊挂断返回页面病情详情消息展示功能完善

## 问题描述

当用户从 `pages_videoConsult/consultRoom/index` 页面发起视频问诊到呼叫页面等待医生接听，在等待过程中挂断返回上一页面时，如果服务端在此期间发送了病情详情消息（MQ消息），这些消息需要能够正确展示在页面上。

## 解决方案

### 1. 挂断返回标记机制

在呼叫页面（`pages_videoConsult/callDoctor/index.js`）中，当用户主动挂断或收到挂断消息时，设置全局标记：

```javascript
// 用户主动挂断
async hangUp() {
  // ... 其他逻辑
  
  // 设置全局标记，表示从呼叫页面返回
  const app = getApp()
  if (app.globalData) {
    app.globalData.returnFromCallPage = true
    console.log('用户主动挂断，设置从呼叫页面返回的标记')
  }

  wx.navigateBack({ delta: 1 })
}

// 收到挂断消息
handleVideoHangUp(messageData) {
  // ... 其他逻辑
  
  setTimeout(() => {
    // 设置全局标记，表示从呼叫页面返回
    const app = getApp()
    if (app.globalData) {
      app.globalData.returnFromCallPage = true
      console.log('设置从呼叫页面返回的标记')
    }
    
    wx.navigateBack({ delta: 1 })
  }, 2000)
}
```

### 2. 视频咨询室页面onShow优化

在视频咨询室页面（`pages_videoConsult/consultRoom/index.js`）的 `onShow` 生命周期中，检查是否从呼叫页面返回：

```javascript
async onShow() {
  // ... 其他逻辑

  if (this.data.consultId && this.data.doctorId) {
    // ... 现有逻辑

    if (needReinitChat) {
      // 重新初始化聊天数据
      this.initChatData()
    } else {
      // 检查是否从呼叫页面返回，如果是则需要刷新消息以获取可能的新消息
      const shouldRefreshMessages = this.checkIfReturnFromCallPage()
      
      if (shouldRefreshMessages) {
        console.log('检测到从呼叫页面返回，刷新消息以获取最新内容')
        this.refreshLatestMessages()
      }
    }
  }
}
```

### 3. 检查返回来源方法

```javascript
/**
 * 检查是否从呼叫页面返回
 * 通过检查全局标记来判断是否刚从呼叫医生页面返回
 */
checkIfReturnFromCallPage() {
  try {
    // 检查是否有标记表示从呼叫页面返回
    const app = getApp()
    if (app.globalData && app.globalData.returnFromCallPage) {
      console.log('检测到从呼叫页面返回的标记')
      // 清除标记，避免重复处理
      app.globalData.returnFromCallPage = false
      return true
    }
    
    return false
  } catch (err) {
    console.error('检查是否从呼叫页面返回时出错:', err)
    return false
  }
}
```

### 4. 刷新最新消息方法

```javascript
/**
 * 刷新最新消息
 * 用于从呼叫页面返回后获取可能的新消息（如病情详情消息）
 */
async refreshLatestMessages() {
  console.log('开始刷新最新消息...')
  
  try {
    // 获取最新的一页消息
    const params = {
      toId: this.data.doctorId,
      patientId: this.data.patientId,
      page: 1, // 获取第一页（最新消息）
      num: 10,
      consultType: 2, // 视频咨询
      consultId: this.data.consultId
    }

    const res = await util.request(api.chatHistory, params, 'POST', 2)
    
    if (res.data.code === 0) {
      const newData = res.data.data.result.reverse()
      
      if (newData.length > 0) {
        // 处理新消息数据，确保relation字段正确
        // 检查是否有新消息需要添加
        // 将新消息添加到现有消息列表
        // 滚动到最新消息
      }
    }
  } catch (err) {
    console.error('刷新最新消息异常:', err)
  }
}
```

### 5. 病情详情消息处理优化

确保病情详情消息（`specificMessageType === 2`）在各种场景下都能正确处理：

#### 实时消息处理
```javascript
addMessageToList(message, callback) {
  // 确保消息有正确的relation字段
  if (message.relation === undefined || message.relation === null) {
    // ... 其他判断逻辑
    
    // 病情详情消息也是医生发送的
    if (message.content && message.content.specificMessageType === 2) {
      message.relation = 1 // 医生发送
    }
  }
}
```

#### 历史消息处理
```javascript
getHistoryData() {
  // ... 在历史消息处理中
  element.messages.forEach(item => {
    // 确保历史消息也有正确的relation字段
    if (item.relation === undefined || item.relation === null) {
      // ... 其他判断逻辑
      
      // 病情详情消息也是医生发送的
      if (item.content && item.content.specificMessageType === 2) {
        item.relation = 1 // 医生发送
      }
    }
  })
}
```

#### 刷新消息处理
```javascript
refreshLatestMessages() {
  // ... 在刷新消息处理中
  element.messages.forEach(item => {
    // 确保消息有正确的relation字段
    if (item.relation === undefined || item.relation === null) {
      // ... 其他判断逻辑
      
      // 病情详情消息也是医生发送的
      if (item.content && item.content.specificMessageType === 2) {
        item.relation = 1 // 医生发送
      }
    }
  })
}
```

## 病情详情消息展示

病情详情消息在模板中的展示条件：

```xml
<!-- 病情详情 -->
<view class="bg-color-white p30 m30" 
      bindtap='goPage' 
      data-id='{{item.content.diseaseId}}' 
      data-url='/pages/illnessDetail/illnessDetail?diseaseId={{item.content.diseaseId}}' 
      data-type="4"
      wx:if='{{item.content.specificMessageType == 2}}'>
  <view class="f32 c333">
    <rich-text nodes='{{item.content.text}}'></rich-text>
  </view>
</view>
```

## 测试场景

1. **正常流程测试**：
   - 进入视频咨询室页面
   - 点击呼叫医生
   - 在等待页面主动挂断
   - 返回视频咨询室页面
   - 验证是否能看到挂断期间服务端发送的病情详情消息

2. **服务端挂断测试**：
   - 进入视频咨询室页面
   - 点击呼叫医生
   - 等待服务端发送挂断消息
   - 自动返回视频咨询室页面
   - 验证是否能看到挂断期间服务端发送的病情详情消息

3. **消息展示测试**：
   - 验证病情详情消息的 `relation` 字段正确设置为 1（医生发送）
   - 验证病情详情消息能正确展示在右侧（医生消息区域）
   - 验证点击病情详情消息能正确跳转到病情详情页面

## 注意事项

1. **全局标记管理**：确保 `returnFromCallPage` 标记在使用后及时清除，避免影响其他页面跳转
2. **消息去重**：刷新消息时需要检查消息ID，避免重复添加已存在的消息
3. **滚动定位**：添加新消息后需要滚动到最新消息位置，提升用户体验
4. **错误处理**：网络异常时不应影响页面正常使用，需要适当的错误处理
5. **性能考虑**：避免频繁刷新消息，只在确实需要时才执行刷新操作

## 相关文件

- `wxapp/pages_videoConsult/callDoctor/index.js` - 呼叫页面挂断逻辑
- `wxapp/pages_videoConsult/consultRoom/index.js` - 视频咨询室页面消息处理
- `wxapp/pages_videoConsult/consultRoom/template/consult.wxml` - 消息展示模板
