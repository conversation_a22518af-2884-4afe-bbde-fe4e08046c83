var util = require('./utils/util.js')
var api = require('./config/api.js')
var Paho = require('./lib/mqtt/paho-mqtt-wx')
var Config = require('./config/index.js')
// 人脸核身
import { initEid } from './mp_ecard_sdk/main'

// MQTT消息类型常量
const MQTT_MESSAGE_TYPES = {
  VIDEO_DIAL: 14, // 视频拨号消息（患者发起）
  VIDEO_ACCEPT: 15, // 视频接诊消息（医生接诊）
  VIDEO_HANGUP: 16, // 视频挂断消息（双方都可能发送）
  DELAY_CONSULT: 17 // 延时问诊消息（医生延长问诊时间）
}

App({
  onLaunch: function(options) {
    console.log(wx.getMenuButtonBoundingClientRect(), 7)
    wx.setNavigationBarTitle({
      title: Config.company
    })
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.screenHeight = res.screenHeight
        this.globalData.windowWidth = res.windowWidth
        this.globalData.statusBarHeight = res.statusBarHeight
        this.globalData.navBarHeight = 44 + res.statusBarHeight
      }
    })
    this.globalData.capsule = wx.getMenuButtonBoundingClientRect() //获取胶囊宽高及位置
    console.log('app.js', options)
    const updateManager = wx.getUpdateManager()
    wx.getUpdateManager().onUpdateReady(function() {
      wx.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success: function(res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate()
          }
        }
      })
    })
    /*  wx.loadFontFace({
		  global: true,
		  family: 'DIN-Regular',
		  source: 'url("https://resources.metamedical.cn/yuanhejia/wx-mini/font/DIN-Regular-2.otf")',
		  success(e) {
		    console.log('字体加载成功', e)
		  },
		  fail(e) {
		    console.log('字体加载失败', e)
		  },
		  complete(e) {
		    console.log('字体加载', e)
		  }
		}) */
    // this.getMessageNum()
    initEid()
  },
  // 计算未读消息
  getMessageNum() {
    var pages = getCurrentPages()
    if (['pages/home/<USER>', 'pages/consult/index/index', 'pages/user/user'].includes(pages[pages.length - 1].route)) {
      const messageRecord = util.getChatData('messageRecord') ? util.getChatData('messageRecord') : {}
      var num = 0
      for (var key in messageRecord) {
        num += messageRecord[key]
      }
      this.getCornerMark(num)
    }

  },

  // 角标设置
  getCornerMark(num) {
    const cartNum = num //默认数据
    if (cartNum != 0) {
      //设置角标
      wx.setTabBarBadge({
        index: 1,
        text: cartNum.toString()
      })
    } else {
      //移除角标
      wx.removeTabBarBadge({
        index: 1
      })
    }
  },
  /**
	 * 获取mqtt连接参数
	 */
  getConnectParams(callback) {
    console.log('调用链接参数api')
    const params = {
      userId: this.globalData.userInfo.userId,
      patientId: this.globalData.userInfo.userId,
      token: this.globalData.userInfo.token
    }
    util.request(api.getConnectParams, params, 'get', 3).then(d => {
      const cdata = d.data
      if (cdata.code == 0) {
        // console.log('请求连接参数数据成功', d, params)
        util.setConnectParams(this, cdata.data)
        // console.log(this.globalData.connectParams, 60)
        // callback && callback();
        this.connectMqtt()
      } else {
        console.log('获取连接参数数据失败', cdata.code)
      }
    })
  },
  /**
	 * 连接 mqtt
	 */
  connectMqtt() {
    // let t = '异常断线';
    var t = {}
    t.i = this.globalData.connectParams.msgTopicName
    t.c = 0
    t.t = 3
    // console.log(t, 98)
    const message = new Paho.Message(JSON.stringify(t))
    message.destinationName = this.globalData.connectParams.willTopicName // willTopicName
    this.connectOptions = {}
    // connectParams.timeout = this.globalData.connectParams.connectionTimeout; 默认30s
    this.connectOptions.userName = api.mqttuser //连接mqtt用户名
    this.connectOptions.password = api.mqttpass //连接mqtt密码
    this.connectOptions.willMessage = message //遗嘱消息
    this.connectOptions.keepAliveInterval = 10 //this.globalData.connectParams.keepAliveInterval; //心跳保持时间
    this.connectOptions.cleanSession = this.globalData.connectParams.cleanSession //断开连接时是否要清除session
    this.connectOptions.reconnect = false //设置如果连接丢失，客户端是否自动尝试重新连接到服务器
    this.connectOptions.onSuccess = this.onConnect //连接成功回调
    this.connectOptions.onFailure = this.failConnect //连接失败回调

    // this.client = new Paho.Client(Util.getConfig.mqttUrl, "wx_client");
    // this.client = new Paho.Client('ws://' + this.globalData.connectParams.host + ':8083/mqtt', this.globalData.connectParams.clientId)
    this.client = new Paho.Client('wss://' + this.globalData.connectParams.dnHost + '/mqtt', this.globalData.connectParams.clientId)

    // console.log('wss://' + this.globalData.connectParams.host + '/mqtt', this.globalData.connectParams.clientId)
    // this.client = new Paho.Client('emqtt.dabaitest.7lk.cn',443,'wx_client');
    this.client.onConnectionLost = this.onConnectionLost
    this.client.onMessageArrived = this.onMessageArrived
    // this.client.onConnected = this.onConnected;
    this.client.connect(this.connectOptions)
    /*this.client.connect({
			userName:Util.getConfig.mqttuser,
			reconnect:false,
			keepAliveInterval:10,
			// password:Util.getConfig.mqttpass,
			onSuccess:this.onConnect,//连接成功回调
			onFailure:this.failConnect,
			willMessage:message
		})*/
  },
  /**
	 * 当消息到达called
	 */
  onMessageArrived: function(message) {
    console.log('mqtt消息到达 - onMessageArrived:' + message.payloadString, message)
    var message = JSON.parse(message.payloadString)
    console.log('message', message)

    // 处理视频相关消息（t=14,15,16）
    if (message.c === 1 && [MQTT_MESSAGE_TYPES.VIDEO_DIAL, MQTT_MESSAGE_TYPES.VIDEO_ACCEPT, MQTT_MESSAGE_TYPES.VIDEO_HANGUP].includes(message.t)) {
      this.handleVideoMessage(message)
      return
    }

    // 处理延时问诊消息（t=17）
    if (message.c === 1 && message.t === MQTT_MESSAGE_TYPES.DELAY_CONSULT) {
      this.handleDelayConsultMessage(message)
      return
    }

    // 判断消息类型：consultType为2是视频问诊，为1或null是图文问诊
    const isVideoConsult = message.consultType === 2
    const isTextConsult = message.consultType === 1 || message.consultType === null || message.consultType === undefined

    let len = 0
    let chatkey = ''

    //回话结束消息
    if (message.c == 1 && message.t == 4) {
      message.sendTime = message.i.endTime
      const endData = JSON.parse(message.i)

      // 根据消息类型设置不同的chatkey
      if (isVideoConsult) {
        chatkey = 'video_' + this.globalData.connectParams.msgTopicName + '_' + endData.toId + '_' + (message.consultId || 'default')
      } else {
        chatkey = this.globalData.connectParams.msgTopicName + '_' + endData.toId
      }

      len = util.getChatData(chatkey).length
      console.log(util.getChatData(chatkey), 157)
      console.log(message, chatkey, len, '========结束回话===========')
    } else if (message.c === 1 && message.t === 12) {
      // 视频问诊消息 - 不计算未读消息，直接跳过
      console.log('收到视频问诊消息(t=12)，跳过未读消息计算:', message)
      // 注意：这里不再更新 messageRecord 和调用 getMessageNum
    } else {
      const pages = getCurrentPages()

      if (pages.length > 2) {
        var prevPage = pages[pages.length - 2]
        console.log(pages, 163)
        if (['pages/consult/chat/chat'].includes(prevPage.route) && isTextConsult) {
          prevPage.setData({
            newMessage: true,
            preview: false
          })
          console.log(prevPage.data.isFirst, '更新数据')
        }
      }

      if (pages.length > 0) {
        const page = pages[pages.length - 1]
        if ('onMessageArrived' in page && page.data.messageArr) {
          let k = 0
          for (let i = 0; i < page.data.messageArr.length; i++) {
            const messages = page.data.messageArr[i].messages
            for (let j = 0; j < messages.length; j++) {
              if (k === 10) {
                break
              }
              if (message.id === messages[j].id) {
                console.log(message, messages[j], 'return')
                return
              }
              k++
            }
            if (k === 10) {
              break
            }
          }
        }
      }

      // 根据消息类型设置不同的chatkey
      if (isVideoConsult) {
        // 视频问诊消息的chatkey格式
        const patientId = message.to?.id || message.from?.id
        const doctorId = message.from?.id || message.to?.id
        const consultId = message.consultId || 'default'
        chatkey = `video_${patientId}_${doctorId}_${consultId}`

        // 为消息添加类型标识（保持原有字段值）
        message.consultType = 2
        message.consultId = consultId
      } else {
        // 图文问诊消息的chatkey格式（保持原有逻辑）
        chatkey = message.content?.specificMessageType
          ? this.globalData.connectParams.msgTopicName + '_' + message.to.id
          : this.globalData.connectParams.msgTopicName + '_' + message.from?.id

        // 为消息添加类型标识（保持原有字段值）
        if (message.consultType === null || message.consultType === undefined) {
          message.consultType = 1
        }
      }

      len = util.getChatData(chatkey).length

      // 只有非视频问诊消息才记录到 messageRecord 中
      if (!isVideoConsult && !message.content?.specificMessageType) {
        const messageRecord = util.getChatData('messageRecord') ? util.getChatData('messageRecord') : {}
        const kay = message.from?.id
        messageRecord[kay] = messageRecord[kay] ? messageRecord[kay] + 1 : 1

        wx.setStorageSync('messageRecord', messageRecord)
        console.log('记录图文问诊未读消息:', {
          doctorId: kay,
          count: messageRecord[kay],
          messageType: message.type
        })

        // 计算未读消息
        this.getMessageNum()
      } else if (isVideoConsult) {
        console.log('收到视频问诊消息，跳过未读消息记录:', {
          type: message.type,
          consultType: message.consultType,
          consultId: message.consultId
        })
      }

      // 正则替换标签 添加class样式
      if (message.type === 10004) {
        message.content.text = message.content.text.replace(/<([\/]?)(font)((:?\s*)(:?[^>]*)(:?\s*))>/g, '<span>')
        message.content.text = message.content.text.replace(/\<span/gi, '<span class="rich-text" ')
      }

      // 只有图文问诊消息才存储到本地缓存
      // 视频问诊消息不使用本地缓存，直接分发给页面处理
      if (!isVideoConsult) {
        util.setChatData(chatkey, message)
      }
    }

    //设置chat.js不更新数据
    message.update = true

    // 分发消息到对应的页面
    this.distributeMessage(message, len, isVideoConsult)
  },

  /**
   * 分发消息到对应的页面
   * @param {Object} message - 消息对象
   * @param {number} len - 消息长度
   * @param {boolean} isVideoConsult - 是否为视频问诊消息
   */
  distributeMessage(message, len, isVideoConsult) {
    console.log('分发消息:', {
      type: message.type,
      consultType: message.consultType,
      consultId: message.consultId,
      isVideoConsult
    })

    const pages = getCurrentPages()
    if (pages.length === 0) {
      console.log('没有活跃页面，无法分发消息')
      return
    }

    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage.route

    console.log('当前页面路由:', currentRoute)

    // 根据消息类型和页面类型进行分发
    if (isVideoConsult) {
      // 视频问诊消息分发给视频咨询室页面或呼叫医生页面
      if (currentRoute === 'pages_videoConsult/consultRoom/index') {
        if (typeof currentPage.onMessageArrived === 'function') {
          console.log('向视频咨询室页面分发消息:', message)
          currentPage.onMessageArrived(message, len)
        } else {
          console.log('视频咨询室页面没有onMessageArrived方法')
        }
      } else if (currentRoute === 'pages_videoConsult/callDoctor/index') {
        // 对于呼叫医生页面，处理需要缓存的消息类型
        const shouldCacheMessage = this.shouldCacheVideoConsultMessage(message)
        if (shouldCacheMessage) {
          console.log('向呼叫医生页面分发需要缓存的消息:', {
            type: message.type,
            specificMessageType: message.content?.specificMessageType,
            reason: shouldCacheMessage.reason
          })
          // 将消息缓存到全局，等返回视频咨询室页面时处理
          this.cacheVideoConsultMessage(message)
        } else {
          console.log('呼叫医生页面忽略不需要缓存的消息:', message.type)
        }
      } else {
        console.log('当前页面不是视频问诊相关页面，忽略视频问诊消息:', currentRoute)
      }
    } else {
      // 图文问诊消息分发给图文咨询页面（保持原有逻辑）
      if (currentRoute === 'pages/consult/chat/chat') {
        if (typeof currentPage.onMessageArrived === 'function') {
          console.log('向图文咨询页面分发消息:', message)
          currentPage.onMessageArrived(message, len)
        } else {
          console.log('图文咨询页面没有onMessageArrived方法')
        }
      } else {
        // 对于非聊天页面，使用原有的通用分发逻辑
        util.onMessageArrived && util.onMessageArrived(message, len)
      }
    }
  },

  /**
   * 处理视频相关消息
   */
  handleVideoMessage(message) {
    console.log('处理视频消息:', message)

    const pages = getCurrentPages()
    if (pages.length === 0) {
      console.log('没有活跃页面，忽略视频消息')
      return
    }

    const currentPage = pages[pages.length - 1]

    // 只向视频呼叫医生页面分发视频消息
    if (currentPage.route === 'pages_videoConsult/callDoctor/index') {
      if (typeof currentPage.onMessageArrived === 'function') {
        console.log('向视频呼叫医生页面分发消息:', message)
        currentPage.onMessageArrived(message)
      } else {
        console.log('视频呼叫医生页面没有onMessageArrived方法')
      }
    } else {
      console.log('当前页面不是视频呼叫医生页面，忽略视频消息:', currentPage.route)
    }
  },

  /**
   * 处理延时问诊消息
   */
  handleDelayConsultMessage(message) {
    console.log('处理延时问诊消息:', message)

    const pages = getCurrentPages()
    if (pages.length === 0) {
      console.log('没有活跃页面，忽略延时问诊消息')
      return
    }

    const currentPage = pages[pages.length - 1]

    // 只向图文问诊聊天页面分发延时问诊消息
    if (currentPage.route === 'pages/consult/chat/chat') {
      if (typeof currentPage.onMessageArrived === 'function') {
        console.log('向图文问诊页面分发延时消息:', message)
        currentPage.onMessageArrived(message)
      } else {
        console.log('图文问诊页面没有onMessageArrived方法')
      }
    } else {
      console.log('当前页面不是图文问诊页面，忽略延时消息:', currentPage.route)
    }
  },

  /**
   * 判断是否需要缓存视频问诊消息
   * @param {Object} message - MQ消息对象
   * @returns {Object|null} 如果需要缓存返回原因对象，否则返回null
   */
  shouldCacheVideoConsultMessage(message) {
    // 助理消息（type=10015, 10016）
    if ([10015, 10016].includes(message.type)) {
      return { reason: '助理消息' }
    }

    // 病情详情消息（specificMessageType == 2）
    if (message.content && message.content.specificMessageType === 2) {
      return { reason: '病情详情消息' }
    }

    // 其他需要缓存的系统消息类型
    if ([10005, 10006, 10007, 10008, 10009, 10010, 10011, 10012, 10013].includes(message.type)) {
      return { reason: '系统消息' }
    }

    // 处方消息（type=16, 17）
    if ([16, 17].includes(message.type)) {
      return { reason: '处方消息' }
    }

    return null
  },

  /**
   * 缓存视频问诊消息
   * 用于在呼叫医生页面时缓存重要消息，等返回视频咨询室页面时处理
   */
  cacheVideoConsultMessage(message) {
    console.log('缓存视频问诊消息:', {
      type: message.type,
      specificMessageType: message.content?.specificMessageType,
      content: typeof message.content === 'string' ? message.content.substring(0, 50) + '...' : message.content
    })

    // 初始化缓存数组
    if (!this.globalData.cachedVideoConsultMessages) {
      this.globalData.cachedVideoConsultMessages = []
    }

    // 添加时间戳，确保消息有sendTime
    if (!message.sendTime) {
      message.sendTime = Date.now()
    }

    // 检查是否已经缓存过相同的消息
    const isDuplicate = this.globalData.cachedVideoConsultMessages.some(cachedMsg => {
      // 根据消息ID判断（如果有的话）
      if (message.id && cachedMsg.id === message.id) {
        return true
      }

      // 根据消息类型和内容判断
      if (cachedMsg.type === message.type) {
        // 对于病情详情消息，比较diseaseId
        if (message.content?.specificMessageType === 2 && cachedMsg.content?.specificMessageType === 2) {
          return cachedMsg.content?.diseaseId === message.content?.diseaseId
        }

        // 对于其他消息，比较内容和时间
        const contentMatch = JSON.stringify(cachedMsg.content) === JSON.stringify(message.content)
        const timeMatch = Math.abs(cachedMsg.sendTime - message.sendTime) < 5000 // 5秒内
        return contentMatch && timeMatch
      }

      return false
    })

    if (!isDuplicate) {
      this.globalData.cachedVideoConsultMessages.push(message)
      console.log('消息已缓存，当前缓存消息数量:', this.globalData.cachedVideoConsultMessages.length)
    } else {
      console.log('消息已存在，跳过缓存')
    }
  },

  /**
   * 获取并清空缓存的视频问诊消息
   * 返回视频咨询室页面时调用此方法获取缓存的消息
   */
  getCachedVideoConsultMessages() {
    const cachedMessages = this.globalData.cachedVideoConsultMessages || []
    console.log('获取缓存的视频问诊消息，数量:', cachedMessages.length)

    // 清空缓存
    this.globalData.cachedVideoConsultMessages = []

    return cachedMessages
  },
  /**
	 * 当客户端连接成功时调用
	 */
  onConnect(e) {
    console.log('mqtt连接成功 - onConnect')
    // over
    this.globalData.isconnect = true
    this.globalData.limit = 0

    console.log(this.globalData.connectParams.msgTopicName, 234)
    // this.client.subscribe(this.globalData.connectParams.msgTopicName)
    const subscribeOptions = {
      qos: 2
    }
    // this.client.subscribe(this.globalData.connectParams.msgTopicName,subscribeOptions)
    this.client.subscribe(this.globalData.connectParams.privatePushTopicName, subscribeOptions)
    console.log('订阅成功')
    // var t = '上线';
    var t = {}
    t.i = this.globalData.connectParams.msgTopicName
    t.c = 0
    t.t = 0
    const message = new Paho.Message(JSON.stringify(t))
    message.destinationName = this.globalData.connectParams.statusTopicName // statusTopicName
    //发送上线消息
    this.client.send(message)
  },
  /**
	 * 当客户端失去连接时调用
	 */
  onConnectionLost: function(responseObject) {
    // wx.closeSocket();
    this.globalData.isconnect = false
    console.log('mqtt失去连接 - responseObject:', responseObject, this.client)
    if (responseObject.errorCode !== 0) {
      console.log('onConnectionLost:', responseObject.errorMessage)
      this.reConnect()
    }
  },
  /**
	 * 当客户端连接失败时调用
	 */
  failConnect(e) {
    this.globalData.isconnect = false
    // wx.showToast({
    //   title: '连接失败',
    //   duration: 1500,
    //   icon: 'none'
    // })
    console.log('mqtt - disConnect - 连接失败', e)
    if (e.errorCode == 8) {
      //断开MQTT连接
      setTimeout(() => {
        // wx.closeSocket();
        this.reConnect()
      }, 0)
    }
  },
  /**
	 * mqtt重新连接
	 * @return {[type]} [description]
	 */
  reConnect() {
    console.log('mqtt重新连接')
    if (this.lockReconnect) return
    this.lockReconnect = true
    clearTimeout(this.timer)
    if (this.globalData.limit < 12) {
      this.timer = setTimeout(() => {
        this.connectMqtt()
        this.lockReconnect = false
      }, 3000)
      this.globalData.limit = this.globalData.limit + 1
    }
  },
  onShow: function(options) {
    const { referrerInfo, scene } = options
    /* 判断是否从eID数字身份⼩程序返回 */
    const { appId } = referrerInfo
    if (scene === 1038 && appId === 'wx0e2cb0b052a91c92') {
      return
    } else {
      // 执⾏接⼊⽅⼩程序原本的逻辑
      util.checkLogin().then(res => {
        this.globalData.hasLogin = true
        util.getTemplate(1)
      }).catch(() => {
        this.globalData.hasLogin = false
      })
      const connectParams = util.getConnectParams(this)
      console.log('onShow--重新连接', this.globalData.connectParams)
      if (connectParams) {
        console.log('onShow----连接mqtt')
        this.lockReconnect = false
        this.connectMqtt()
      }
    }
  },
  /**
	 * 断开MQTT 发送离线消息
	 */
  onHide() {
    console.log('onHide--断开连接')
    // var t = '下线';
    var t = {}
    t.i = this.globalData.connectParams.msgTopicName
    t.c = 0
    t.t = 1
    const message = new Paho.Message(JSON.stringify(t))
    message.destinationName = this.globalData.connectParams.statusTopicName
    //发送离线消息
    this.client.send(message)
    this.lockReconnect = true
    //断开MQTT连接
    setTimeout(() => {
      wx.closeSocket()
    }, 0)
  },
  globalData: {
    statusBarHeight: 0,
    screenHeight: 0,
    hasLogin: false,
    userInfo: wx.getStorageSync('userInfo'),
    baseInfo: {},
    connectParams: null, //连接参数
    isconnect: false, //是否连接mqtt
    limit: 0,
    consultType: 1, //跳转到咨询tabbar页面传递的参数
    doctorName: '',
    templateId: [],
    peopleList: [], //就诊人列表
    loginNum: 0,
    cachedVideoConsultMessages: [] //缓存的视频问诊消息（用于呼叫页面时缓存助理消息）
  }
})
